{"name": "taskunify-backend", "version": "1.0.0", "description": "TaskUnify Backend API", "author": "TaskUnify Team", "private": true, "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "worker": "node dist/worker", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:deploy": "prisma migrate deploy", "prisma:studio": "prisma studio", "prisma:seed": "ts-node prisma/seed.ts", "db:reset": "prisma migrate reset --force"}, "dependencies": {"@nestjs/common": "^10.3.0", "@nestjs/core": "^10.3.0", "@nestjs/platform-express": "^10.3.0", "@nestjs/config": "^3.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.2", "@nestjs/swagger": "^7.1.17", "@nestjs/throttler": "^5.1.1", "@nestjs/schedule": "^4.0.0", "@nestjs/bull": "^10.0.1", "@prisma/client": "^5.7.1", "prisma": "^5.7.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-google-oauth20": "^2.0.0", "bcryptjs": "^2.4.3", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "bull": "^4.12.2", "ioredis": "^5.3.2", "axios": "^1.6.2", "helmet": "^7.1.0", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "express-rate-limit": "^7.1.5", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "crypto-js": "^4.2.0", "uuid": "^9.0.1", "date-fns": "^3.0.6", "lodash": "^4.17.21", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^10.2.1", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.3.0", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/node": "^20.10.4", "@types/supertest": "^2.0.16", "@types/bcryptjs": "^2.4.6", "@types/passport-jwt": "^3.0.13", "@types/passport-google-oauth20": "^2.0.14", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/crypto-js": "^4.2.1", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/cookie-parser": "^1.4.6", "@types/compression": "^1.7.5", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.1.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}